from app.core.task_framework import tasks_collection_name
from app.connection.establish_db_connection import get_mongo_db, get_node_db
from fastapi import APIRouter
from typing import Dict, Any, List
import sys
import asyncio
import pkgutil
import importlib

from app.core.task_framework import TaskStatus
from app.core.Settings import settings
# Discover all classes in the 'app.discussions.types' package
# Import the package correctly. Assuming 'discussions.types' is within a parent package 'discussions'.

from app.agents.ai_architect import ArchitectureAgent
from app.agents.ai_projectmanager import ProjectManagerAgent
from app.agents.ai_softwaredesigner import SoftwareDesignerAgent
from app.agents.ai_epicmanager import EpicManagerAgent
from app.agents.ai_userstorymanager import UserStoryManagerAgent
from app.agents.ai_interfacemanager import InterfaceManagerAgent
from app.agents.ai_containeropsmanager import ContainerOpsAgent
from app.agents.ai_testcasegenerator import TestCaseGeneratorAgent
from app.agents.ai_componenttestcasegenerator import ComponentTestCaseGeneratorAgent
from app.agents.supervisor import Supervisor
from app.telemetry.logger_config import get_logger,set_task_id
from app.agents.ai_documentation import DocumentationAgent
from app.utils.knowledge_utils import initialize_code_query
from app.discussions.discussion_context import auto_config_session_context
from app.connection.tenant_middleware import get_tenant_id
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import settings
from app.utils.datetime_utils import generate_timestamp
import os
update_logger_agent = get_logger('app.agents')

async def get_autoconfig_user_budget_usd(user_id: str, tenant_id: str) -> float:
    """
    Get user's remaining budget in USD for auto-configuration
    Separate function to avoid circular imports with app.tasks

    Args:
        user_id (str): The user ID
        tenant_id (str): The tenant ID

    Returns:
        float: Remaining budget in USD
    """
    try:
        print(generate_timestamp(), f"💰 Getting budget for user: {user_id}, tenant: {tenant_id}")

        # Check if it's B2C tenant
        is_b2c = tenant_id == settings.KAVIA_B2C_CLIENT_ID
        print(generate_timestamp(), f"🏢 Is B2C tenant: {is_b2c}")

        if not is_b2c:
            print(generate_timestamp(), f"⚠️ Not a B2C tenant, returning unlimited budget")
            return float('inf')  # Unlimited budget for non-B2C

        # Get MongoDB connection for root database
        KAVIA_ROOT_DB_NAME = "kavia_root"
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        collection = mongo_db.db["active_subscriptions"]

        print(generate_timestamp(), f"🔍 Fetching latest subscription from database...")

        # Find the latest subscription for the user
        latest_subscription = collection.find_one(
            {"user_id": user_id},
            sort=[("created_at", -1)]
        )

        if not latest_subscription:
            print(generate_timestamp(), f"❌ No subscription found for user {user_id}, returning $0 budget")
            return 0.0

        # Get total credits from subscription
        total_credits = latest_subscription.get("credits", 0)
        print(generate_timestamp(), f"📊 Total credits from subscription: {total_credits}")

        # Get current cost from usage collection
        usage_collection = mongo_db.db["usage"]
        current_cost_usd = 0.0

        usage_record = usage_collection.find_one({"user_id": user_id})
        if usage_record:
            current_cost_usd = usage_record.get("total_cost_usd", 0.0)

        print(generate_timestamp(), f"💵 Current cost (USD): {current_cost_usd}")

        # Calculate used credits (1 USD = 20,000 credits)
        CREDITS_PER_USD = 20000
        used_credits = current_cost_usd * CREDITS_PER_USD

        print(generate_timestamp(), f"🔢 Used credits: {current_cost_usd} * {CREDITS_PER_USD} = {used_credits}")

        # Calculate remaining credits
        remaining_credits = max(0, total_credits - used_credits)

        print(generate_timestamp(), f"🎯 Remaining credits: {total_credits} - {used_credits} = {remaining_credits}")

        # Convert remaining credits to USD budget
        budget_usd = remaining_credits / CREDITS_PER_USD

        print(generate_timestamp(), f"💰 Final budget: {remaining_credits} / {CREDITS_PER_USD} = ${budget_usd:.6f}")

        return round(budget_usd, 6)

    except Exception as e:
        print(generate_timestamp(), f"❌ Error getting user budget: {str(e)}")
        return 0.0

package = importlib.import_module("app.discussions.types")

for loader, module_name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + '.'):
    # Ensure the imported modules are correctly referenced with their full package names
    importlib.import_module(module_name)
# Discover and register all classes in the 'app.discussions.types' package


tasks = {}

_SHOW_NAME = "agent"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

from typing import Dict, Any, List

def parse_config(config: Dict[str, Any], node_type: str) -> tuple:
    # Extract config types from the configuration
    config_types = []
    
    def extract_config_types(conf, prefix=''):
        if conf.get('configure', False):
            config_type = f"{prefix}autoconfig"
            if config_type not in config_types:
                config_types.append(config_type)
            
            # Remove generic 'autoconfig' if present
            if 'autoconfig' in config_types:
                config_types.remove('autoconfig')
        
        # Recursively process nested configs
        for key, value in conf.items():
            if isinstance(value, dict):
                extract_config_types(value, f"{key}_")
    
    # Process the config to extract types
    extract_config_types(config)
    
    # Load mapping.json to determine the priority of config types
    import os
    import json
    
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    mapping_path = os.path.join(base_dir, 'agents', 'mapping.json')
    
    with open(mapping_path, 'r') as file:
        mapping = json.load(file)
    
    # Find all configs that exist in mapping.json
    valid_configs = []
    for config_type in config_types:
        if config_type in mapping:
            valid_configs.append(config_type)
    
    # Find the config with the highest priority in the mapping
    start_config = None
    
    # Create a list of all keys in mapping to determine order/priority
    mapping_keys = list(mapping.keys())
    
    if valid_configs:
        # Sort valid configs based on their position in the mapping
        valid_configs.sort(key=lambda x: mapping_keys.index(x) if x in mapping_keys else float('inf'))
        start_config = valid_configs[0]
    elif config_types:
        # Use the first config from the list anyway, even if not in mapping
        start_config = config_types[0]
    else:
        # Fallback if no configs specified
        start_config = f"{node_type.lower()}_autoconfig"
        config_types.append(start_config)
    
    # Log the final config types
    print(f"Config types: {config_types}")
    print(f"Start config: {start_config}")

        # Add the specific mappings as requested
    if 'architecture_autoconfig' in config_types:
        config_types.remove('architecture_autoconfig')

    # if 'system_context_autoconfig' in config_types:
    #     config_types.append('system_context_config')
    #     config_types.remove('system_context_autoconfig')
    
    # if 'container_autoconfig' in config_types:
    #     config_types.append('container_config')
    #     config_types.remove('container_autoconfig')
    
    # if 'architectural_requirements_autoconfig' in config_types:
    #     config_types.append('capture_requirements')
    #     config_types.remove('architectural_requirements_autoconfig')
    
    # if 'system_context_autoconfig' in config_types:
    #     config_types.append('system_context_config')
    #     config_types.remove('system_context_autoconfig')
    
    # if 'container_autoconfig' in config_types:
    #     config_types.append('container_config')
    #     config_types.remove('container_autoconfig')

    
    return config_types, start_config

# def parse_config(config: Dict[str, Any], node_type: str) -> tuple:
#     config_types = []
#     start_config = None

#     def check_config(conf, prefix=''):
#         if conf.get('configure', False):
#             config_type = f"{prefix}autoconfig"
#             # config_type = f"{prefix}autoconfig" if prefix else f"{node_type.lower()}_autoconfig"
            
#             if config_type not in config_types:
#                 config_types.append(config_type)
        
#             if 'autoconfig' in config_types:
#                 config_types.remove('autoconfig')
                
#         for key, value in conf.items():
#             if isinstance(value, dict):
#                 check_config(value, f"{key}_")

#     check_config(config)

#     # Determine start_config
#     if config_types:
#         start_config = config_types[0]

#         if start_config == 'architectural_requirements_autoconfig':
#             start_config = 'capture_requirements'
#             config_types[0] = 'capture_requirements'
#         if start_config == 'requirementroot_autoconfig':
#             start_config = 'requirements_autoconfig'
#             config_types[0] = 'requirements_autoconfig'
#         if start_config == 'architectureroot_autoconfig':
#             start_config = 'capture_requirements'
#             config_types[0] = 'capture_requirements'
#             # Add C4 model specific config types
#             if 'system_context_config' not in config_types:
#                 config_types.append('system_context_config')
#             if 'container_config' not in config_types:
#                 config_types.append('container_config')
#         if start_config == 'architecture_autoconfig':
#             start_config = 'capture_requirements'
#             config_types[0] = 'capture_requirements'
#             # Add C4 model specific config types here as well
#             if 'system_context_config' not in config_types:
#                 config_types.append('system_context_config')
#             if 'container_config' not in config_types:
#                 config_types.append('container_config')
#     else:
#         start_config = f"{node_type.lower()}_autoconfig"
#         config_types.append(start_config)

#     if 'architectural_requirements_autoconfig' in config_types:
#         config_types.append('capture_requirements')

#     if 'system_context_autoconfig' in config_types:
#         config_types.append('system_context_config')

#     if 'container_autoconfig' in config_types:
#         config_types.append('container_config')

#     if 'architectural_requirements_autoconfig' in config_types:
#         config_types.append('capture_requirements')

#     if 'system_context_autoconfig' in config_types:
#         config_types.append('system_context_config')

#     if 'container_autoconfig' in config_types:
#         config_types.append('container_config')

#     # Add specific intermediate steps
#     config_types = add_specific_intermediate_steps(config_types)
    
#     update_logger_agent.info(f"Config types after parsing: {config_types}")
#     return config_types, start_config

def add_specific_intermediate_steps(config_types: List[str]) -> List[str]:
    # Define dependencies for each configuration type
    dependencies = {
        'component_autoconfig': [
          
          
        ],
        'component_testcase_autoconfig': [
           
           
        ],
        'sub_components_autoconfig': [
           
           
        ],
        'requirements_autoconfig': [
           
           
        ],
        'architecture_autoconfig': [

           
        ],
        'ArchitectureRoot_autoconfig': [
           
           
        ],
        'requirementroot_autoconfig': [
                      
            
        ],
        'capture_requirements': [
        ]
    }

    result = []
    for config in config_types:
        # Add dependencies if they're not already in the result
        if config in dependencies:
            for dep in dependencies[config]:
                if dep not in result:
                    result.append(dep)
        
        # Add the current config if it's not already in the result
        if config not in result:
            result.append(config)

    return result

async def configure_node(node_type: str, node_id:int, user_level:int, request: dict,task_id:str):
    update_logger_agent = get_logger("app.agents.agent_main")
    set_task_id(task_id)
    

    print(f"\n{'='*50}\n[Configure Node Start] Task ID: {task_id}\n{'='*50}")
    print(f"Initial Parameters:")
    print(f"  Node Type: {node_type}")
    print(f"  Node ID: {node_id}")
    print(f"  User Level: {user_level}")
    print(f"  Request: {str(request)}")

    # Check if repoDetails and projectId are available
    repoDetails = request.get("repo", None)
    projectId = request.get("projectId", None)
    
    session_id = None 
    if repoDetails and projectId:
        update_logger_agent.info(f"Initializing code query with repoDetails and projectId: {projectId}")
        try:
            # initiate a knowledge session
            session_data = await initialize_code_query(repoDetails, projectId, task_id)
            if session_data.get('session_id'):
                session_id = session_data.get('session_id')
                auto_config_session_context.set(session_id)
                update_logger_agent.info(f"Initialized session_id for code-extraction: {session_id}")
            else:
                # If session_id is not available even though repoDetails and projectId are provided, raise exception
                error_msg = f"Failed to initialize code query session_id"
                update_logger_agent.info(error_msg)
                raise Exception(error_msg)
        except Exception as e:
            error_msg = f"--------------------Error initializing code query---------------------------------"
            update_logger_agent.info(error_msg)
            raise Exception(error_msg)
    else:
        update_logger_agent.info("Skipping code query initialization: repo details or project ID not provided")
    
    update_logger_agent.info(f"Starting configuration for node type: {node_type}, node ID: {node_id}")
    
    try:
        node_id = node_id 
        project_id = node_id
        print("[MongoDB Setup]")
        mongo_db = get_mongo_db(settings.MONGO_DB_NAME, tasks_collection_name)
        await mongo_db.set_collection(tasks_collection_name)
        
        print("[Configuration Parsing]")
        config_types, start_config = parse_config(request, node_type)
        
        # Save config_types in MongoDB task document
        await mongo_db.update_by_task_id(task_id, {
            'node_id': node_id, 
            'run_completed': False,
            'config_types': config_types,
            'completed_configs': [],  # Initialize empty list for completed configs
            'progress': 0  # Initialize progress at 0%
        })

        
        print("[Configuration Parsing]")
        config_types, start_config = parse_config(request, node_type)
        
        # Save config_types in MongoDB task document
        await mongo_db.update_by_task_id(task_id, {
            'node_id': node_id, 
            'run_completed': False,
            'config_types': config_types,
            'completed_configs': [],  # Initialize empty list for completed configs
            'progress': 0  # Initialize progress at 0%
        })

        logging_context = {
            'mongo_handler': mongo_db,
            'task_id': task_id,
        }
        print("MongoDB task entry created/updated")
        update_logger_agent = get_logger("agent_main")
        if logging_context:
            logging_context = logging_context
            task_id = logging_context.get('task_id')
            set_task_id(task_id)
            print(f"Logging context set with task ID: {task_id}")  # Console output
            update_logger_agent.info(f"Logging context set with task ID: {task_id}")
        
        else:
            logging_context = {}
            task_id = None
            mongo_handler = None
        task_status = TaskStatus.IN_PROGRESS
        print(f"Task status set to: {task_status}")  # Console output
        update_logger_agent.info(f"Task status set to: {task_status}")

        print("[Agent Initialization]")
        semaphore = asyncio.Semaphore(1)
        print("Initializing agents...")  # Console output
        update_logger_agent.info("Initializing agents...")
        # supervisor = Supervisor(name="Supervisor", agents={...}, user_level=user_level)

        project_manager = ProjectManagerAgent(name="project_manager",node_id=node_id,  node_type='Project',  root_node_type='Project',  discussion_type='configuration',levels = 2,semaphore = semaphore,logging_context =logging_context)
        
        DesignerAgent = SoftwareDesignerAgent(name="DesignerAgent",node_id=node_id,  node_type='Architecture',  root_node_type='Product',  discussion_type='autoconfig',  levels = 2,semaphore = semaphore,logging_context =logging_context)
        
        architecture_agent = ArchitectureAgent(name="architecture_agent",node_id=node_id,node_type='Architecture',root_node_type='Product',discussion_type='autoconfig',logging_context=logging_context,levels=3,semaphore=semaphore)
        
        epic_agent = EpicManagerAgent(name="epic_agent",node_id=node_id,node_type='Epic',root_node_type='Product',discussion_type='autoconfig',logging_context=logging_context,levels=3,semaphore=semaphore)
        
        userstory_agent = UserStoryManagerAgent(name="userstory_agent",node_id=node_id,node_type='UserStory',root_node_type='Product',discussion_type='autoconfig',logging_context=logging_context,levels=3,semaphore=semaphore)
        
        interface_agent = InterfaceManagerAgent(name="interface_agent",node_id=node_id,node_type='Interface',root_node_type='Product',discussion_type='autoconfig',logging_context=logging_context,levels=3,semaphore=semaphore)

        documentation_agent = DocumentationAgent(name="documentation_agent", node_id=node_id, node_type='Documentation', root_node_type='Product', discussion_type='autoconfig', logging_context=logging_context, levels=3, semaphore=semaphore)

        container_ops_agent= ContainerOpsAgent(name="container_ops_agent",node_id=node_id,node_type='Container',root_node_type='Project',discussion_type='autoconfig',logging_context=logging_context,levels=3,semaphore=semaphore)
        testcase_generator = TestCaseGeneratorAgent(
        name="testcase_generator",
        node_id=node_id,
        node_type='UserStory',
        root_node_type='Project',
        discussion_type='testcase_generation',
        logging_context=logging_context,
        levels=3,
        semaphore=semaphore
        )
        
        component_testcase_generator = ComponentTestCaseGeneratorAgent(
        name="component_testcase_generator",
        node_id=node_id,
        node_type='Component',
        root_node_type='Project',
        discussion_type='component_testcase_generation',
        logging_context=logging_context,
        levels=3,
        semaphore=semaphore
        )
        print("All agents initialized successfully")  # Console output
        update_logger_agent.info("All agents initialized successfully")

        
        

        print(f"Parsed configuration. Start config: {start_config}, Config types: {config_types}")  # Console output
        update_logger_agent.info(f"Parsed configuration. Start config: {start_config}, Config types: {config_types}")
        update_logger_agent.info(f"Start config: {start_config}")
        update_logger_agent.info(f"Base config types: {config_types}")
        
        if 'system_context_config' in config_types:
            update_logger_agent.info("System context configuration is included")
        if 'container_config' in config_types:
            update_logger_agent.info("Container configuration is included")

        # Fetch user budget for supervisor
        print("[Budget Fetching]")
        update_logger_agent.info("Fetching user budget for supervisor")
        try:
            user_id = os.environ.get("user_id", "admin")
            tenant_id = get_tenant_id()
            budget = await get_autoconfig_user_budget_usd(user_id, tenant_id)
            print(f"*****************************Fetched budget: ${budget:.6f} for user: {user_id}, tenant: {tenant_id}")
            update_logger_agent.info(f"Fetched budget: ${budget:.6f} for user: {user_id}, tenant: {tenant_id}")
        except Exception as e:
            budget = None
            print(f"Error fetching budget: {str(e)}, using unlimited budget")
            update_logger_agent.warning(f"Error fetching budget: {str(e)}, using unlimited budget")

        print("[Supervisor Setup]")
        supervisor = Supervisor(
        name="Supervisor",
        agents={
            "ArchitectureAgent": architecture_agent,
            "ProjectAgent": project_manager,
            "DesignerAgent": DesignerAgent,
            "epic_agent": epic_agent,
            "userstory_agent": userstory_agent,
            "interface_agent": interface_agent,
            "TestCaseGeneratorAgent": testcase_generator,
            "documentation_agent": documentation_agent,
            "container_ops_agent": container_ops_agent,
            "ComponentTestCaseGeneratorAgent": component_testcase_generator
        },
        user_level=3,
        architecture_level=3,
        start_config=start_config,
        config_types=config_types,
        budget=budget
                )
        
        print("Supervisor initialized successfully")
        print(start_config, config_types)

        # deleted_count = await db.delete_architecture_nodes(project_id)

        await supervisor.start(node_id, node_type, project_id)

        print("Supervisor started successfully")
        # supervisor = Supervisor(name="Supervisor", agents={"ArchitectureAgent": architecture_agent,"ProjectAgent": project_manager,"DesignerAgent": DesignerAgent,"epic_agent": epic_agent,"userstory_agent": userstory_agent},user_level=3, start_config="project_autoconfig")
        # await supervisor.start(node_id, node_type, project_id)
        

        project_manager.supervisor = supervisor  
        architecture_agent.supervisor = supervisor
        DesignerAgent.supervisor = supervisor
        epic_agent.supervisor = supervisor
        userstory_agent.supervisor = supervisor
        interface_agent.supervisor = supervisor
        documentation_agent.supervisor = supervisor
        container_ops_agent.supervisor = supervisor
        testcase_generator.supervisor =supervisor
        component_testcase_generator.supervisor = supervisor

        print("Creating and starting supervisor run task")  # Console output
        update_logger_agent.info("Creating and starting supervisor run task")
        print("[Task Execution] - create_task")
        run_task = asyncio.create_task(supervisor.run())
        print("Supervisor run task created")

        termination_work_item = supervisor.create_termination_work_item()
        await supervisor.add_work_item(termination_work_item)
        print("Termination work item added")

        print("Waiting for run task to complete")  # Console output
        update_logger_agent.info("Waiting for run task to complete")
        await run_task
        print("Run task completed successfully")
        print("[Final Timing Report]")
        
        print("Run task completed successfully")  # Console output
        update_logger_agent.info("Run task completed successfully")
        await supervisor.print_final_timing_report()

        print("Timing report generated")

    except Exception as e:
        print(e)
        task_status = TaskStatus.FAILED
        update_logger_agent.info(f"Error occurred during configuration: {str(e)}", exc_info=True)
        print(f"Error occurred during configuration: {str(e)}")
    finally:

        task_configuration = await mongo_db.get_by_task_id(task_id)
        task_status = task_configuration['status']

        try:
            if task_status == TaskStatus.FAILED:
                await mongo_db.update_by_task_id(task_id, {'node_id': node_id, 'task_status': TaskStatus.FAILED, 'run_completed': True})
                print(f"Task {task_id} marked as FAILED")  # Console output
                update_logger_agent.info(f"Task {task_id} marked as FAILED")
            else:
                await mongo_db.update_by_task_id(task_id, {'node_id': node_id, 'task_status': TaskStatus.COMPLETE, 'run_completed': True})
                print(f"Task {task_id} marked as COMPLETE")  # Console output
                update_logger_agent.info(f"Task {task_id} marked as COMPLETE")
        except Exception as e:
            print(f"Error updating task status in MongoDB: {str(e)}", file=sys.stderr)  # Console error output
            update_logger_agent.info(f"Error updating task status in MongoDB: {str(e)}", exc_info=True)
        
        # await supervisor.print_final_timing_report()
        print(f"Configuration process completed for task {task_id}")  # Console output
        update_logger_agent.info(f"Configuration process completed for task {task_id}")
        return {'task_id': task_id}